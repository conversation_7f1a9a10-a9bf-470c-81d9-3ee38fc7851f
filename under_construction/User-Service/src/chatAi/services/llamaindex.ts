interface LlamaIndexResponse {
  id: string;
  status?: string;
  project_id?: string;
  file_id?: string;
  pipeline_id?: string;
  error?: string;
}

interface RetrieveResponse {
  nodes: Array<{
    text: string;
    metadata: {
      page?: number;
      source?: string;
      file_name?: string;
    };
    score: number;
  }>;
}

interface ProjectResponse {
  id: string;
  name: string;
  description?: string;
}

interface PipelineResponse {
  id: string;
  name: string;
  project_id?: string;
}

interface RetrieverResponse {
  id: string;
  name: string;
  project_id: string;
}

/**
 * LlamaIndex Service for vector indexing and retrieval
 * Based on working reference implementation from /referenceCode/
 */
export class LlamaIndexService {
  private apiKey: string;
  private baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
  private isConfigured: boolean;

  constructor() {
    // Use the working API key from reference implementation
    this.apiKey = 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp';
    this.isConfigured = !!this.apiKey;
    console.log('🔑 LlamaCloud API Key:', this.apiKey.substring(0, 10) + '...');

    if (!this.apiKey) {
      console.warn(
        '⚠️  LLAMA_CLOUD_API_KEY not configured. Vector indexing will be disabled.',
      );
    } else {
      console.log('✅ LlamaIndexService initialized successfully');
    }
  }

  checkConfiguration(): void {
    if (!this.isConfigured) {
      throw new Error(
        'LlamaIndex service is not configured. Please set LLAMA_CLOUD_API_KEY.',
      );
    }
  }

  /**
   * Get the actual project associated with the API key
   */
  async getOrCreateDefaultProject(): Promise<ProjectResponse> {
    try {
      // First, list all projects to see what's actually available with this API key
      console.log('🔍 Checking projects associated with API key...');
      const projects = await this.listProjects();

      if (projects && projects.length > 0) {
        const project = projects[0];
        console.log(`✅ Using project: ${project.id} (${project.name})`);
        return project;
      } else {
        console.log('📋 No projects found, creating tosky_test project...');
        return await this.createProject('tosky_test');
      }
    } catch (error) {
      console.error('❌ Error accessing projects:', (error as Error).message);
      throw error;
    }
  }

  /**
   * List existing pipelines (all pipelines accessible with this API key)
   */
  async listPipelines(): Promise<PipelineResponse[]> {
    this.checkConfiguration();

    try {
      const response = await fetch(`${this.baseUrl}/pipelines`, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex listPipelines error: ${response.status} - ${errorText}`,
        );
        return [];
      }

      const result = await response.json();
      console.log(`📋 Found ${result.length || 0} existing pipelines`);
      return result;
    } catch (error) {
      console.error(
        `❌ LlamaIndex listPipelines error:`,
        (error as Error).message,
      );
      return [];
    }
  }

  /**
   * Get or create the default tosky_test pipeline
   */
  async getOrCreateDefaultPipeline(
    projectId: string,
  ): Promise<PipelineResponse> {
    this.checkConfiguration();

    try {
      // First, try to list existing pipelines
      const existingPipelines = await this.listPipelines();

      // Look for tosky_test pipeline
      const toskyPipeline = existingPipelines.find(
        (p) => p.name === 'tosky_test',
      );
      if (toskyPipeline) {
        console.log(
          `♻️  Found existing tosky_test pipeline: ${toskyPipeline.id}`,
        );
        return toskyPipeline;
      }

      // If no tosky_test pipeline found, use the first available pipeline
      if (existingPipelines && existingPipelines.length > 0) {
        const pipeline = existingPipelines[0];
        console.log(
          `♻️  Using existing pipeline: ${pipeline.id} (${pipeline.name})`,
        );
        return pipeline;
      }

      // Create a new default pipeline if none exists
      console.log(`📝 Creating new default pipeline for project: ${projectId}`);
      const timestamp = Date.now();
      const pipelineName = `default_pipeline_${timestamp}`;
      return await this.createPipeline(pipelineName, projectId);
    } catch (error) {
      console.error(
        `❌ Error getting/creating default pipeline:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Create project for organizing documents
   */
  async createProject(projectName: string): Promise<ProjectResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name: projectName,
        description: `Project for ${projectName}`,
      };

      const response = await fetch(`${this.baseUrl}/projects`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex createProject error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Project creation failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ Project created: ${result.id}`);
      return result;
    } catch (error) {
      console.error(
        `❌ LlamaIndex createProject error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * List all projects
   */
  async listProjects(): Promise<ProjectResponse[]> {
    this.checkConfiguration();

    try {
      const response = await fetch(`${this.baseUrl}/projects`, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `List projects failed: ${response.status} - ${errorText}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error(
        `❌ LlamaIndex listProjects error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Upload file to project
   */
  async uploadFile(
    fileBuffer: Buffer,
    filename: string,
    projectId: string,
  ): Promise<any> {
    this.checkConfiguration();

    try {
      const formData = new FormData();
      const uint8Array = new Uint8Array(fileBuffer);
      const blob = new Blob([uint8Array], {
        type: this.getContentType(filename),
      });
      formData.append('upload_file', blob, filename);
      formData.append('project_id', projectId);

      const response = await fetch(`${this.baseUrl}/files`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex uploadFile error: ${response.status} - ${errorText}`,
        );
        throw new Error(`File upload failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ File uploaded: ${result.id}`);
      return result;
    } catch (error) {
      console.error(
        `❌ LlamaIndex uploadFile error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Create processing pipeline (without files initially)
   */
  async createPipeline(
    pipelineName: string,
    projectId: string,
  ): Promise<PipelineResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name: pipelineName,
        project_id: projectId,
      };

      const response = await fetch(`${this.baseUrl}/pipelines`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex createPipeline error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Pipeline creation failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ Pipeline created: ${result.id}`);
      return result;
    } catch (error) {
      console.error(
        `❌ LlamaIndex createPipeline error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Add files to existing pipeline
   */
  async addFilesToPipeline(
    pipelineId: string,
    fileIds: string[],
  ): Promise<void> {
    this.checkConfiguration();

    try {
      const payload = {
        file_ids: fileIds,
      };

      const response = await fetch(
        `${this.baseUrl}/pipelines/${pipelineId}/files`,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex addFilesToPipeline error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Adding files to pipeline failed: ${response.status}`);
      }

      console.log(`✅ Files added to pipeline: ${pipelineId}`);
    } catch (error) {
      console.error(
        `❌ LlamaIndex addFilesToPipeline error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Sync pipeline to process files and create vector embeddings
   */
  async syncPipeline(pipelineId: string): Promise<void> {
    this.checkConfiguration();

    try {
      const response = await fetch(
        `${this.baseUrl}/pipelines/${pipelineId}/sync`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex syncPipeline error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Pipeline sync failed: ${response.status}`);
      }

      console.log(`✅ Pipeline sync initiated: ${pipelineId}`);
    } catch (error) {
      console.error(
        `❌ LlamaIndex syncPipeline error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Check pipeline status
   */
  async checkPipelineStatus(pipelineId: string): Promise<any> {
    this.checkConfiguration();

    try {
      const response = await fetch(
        `${this.baseUrl}/pipelines/${pipelineId}/status`,
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
          },
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex checkPipelineStatus error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Pipeline status check failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`📊 Pipeline status: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      console.error(
        `❌ LlamaIndex checkPipelineStatus error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Wait for pipeline sync to complete
   */
  async waitForPipelineSync(
    pipelineId: string,
    maxWaitTime: number = 300000,
  ): Promise<boolean> {
    const startTime = Date.now();
    const pollInterval = 5000; // 5 seconds

    console.log(`⏳ Waiting for pipeline sync to complete: ${pipelineId}`);

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const status = await this.checkPipelineStatus(pipelineId);

        if (status.status === 'COMPLETED' || status.status === 'SUCCESS') {
          console.log(`✅ Pipeline sync completed: ${pipelineId}`);
          return true;
        } else if (status.status === 'FAILED' || status.status === 'ERROR') {
          console.error(`❌ Pipeline sync failed: ${pipelineId}`);
          return false;
        }

        console.log(`⏳ Pipeline still processing... Status: ${status.status}`);
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      } catch (error) {
        console.warn(
          `⚠️ Error checking pipeline status: ${(error as Error).message}`,
        );
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      }
    }

    console.warn(`⏰ Pipeline sync timeout: ${pipelineId}`);
    return false;
  }

  /**
   * Create retriever for querying
   */
  async createRetriever(
    retrieverName: string,
    projectId: string,
    pipelineIds: string[],
  ): Promise<RetrieverResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name: retrieverName,
        project_id: projectId,
        pipeline_ids: pipelineIds,
      };

      const response = await fetch(`${this.baseUrl}/retrievers`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex createRetriever error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Retriever creation failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ Retriever created: ${result.id}`);
      return result;
    } catch (error) {
      console.error(
        `❌ LlamaIndex createRetriever error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Create vector index from parsed document using proper pipeline sync workflow
   */
  async createIndex(
    parsedData: any,
    documentName: string,
  ): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      console.log(`🔍 Creating vector index for: ${documentName}`);

      // Step 1: Get or create default project (reuse due to API limitations)
      const project = await this.getOrCreateDefaultProject();

      // Step 2: Create file buffer from parsed text
      const textBuffer = Buffer.from(parsedData.text, 'utf-8');

      // Step 3: Upload file
      const file = await this.uploadFile(textBuffer, documentName, project.id);

      // Step 4: Create a new pipeline for this document with unique name
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const uuid = Math.random().toString(36).substring(2, 15);
      const pipelineName = `pipeline_${timestamp}_${randomId}_${uuid}`;
      const pipeline = await this.createPipeline(pipelineName, project.id);

      // Step 5: Add file to pipeline
      await this.addFilesToPipeline(pipeline.id, [file.id]);

      // Step 6: Sync pipeline to create vector embeddings (CRITICAL STEP!)
      await this.syncPipeline(pipeline.id);

      // Step 7: Wait for pipeline sync to complete
      const syncCompleted = await this.waitForPipelineSync(pipeline.id);
      if (!syncCompleted) {
        throw new Error('Pipeline sync failed or timed out');
      }

      // Step 8: Create retriever with highly unique name
      const timestamp2 = Date.now();
      const randomId2 = Math.random().toString(36).substring(2, 15);
      const uuid2 = Math.random().toString(36).substring(2, 15);
      const retrieverName = `retr_${timestamp2}_${randomId2}_${uuid2}`;
      const retriever = await this.createRetriever(retrieverName, project.id, [
        pipeline.id,
      ]);

      console.log(`✅ Vector index created with retriever: ${retriever.id}`);

      return {
        id: retriever.id,
        project_id: project.id,
        file_id: file.id,
        pipeline_id: pipeline.id,
        status: 'SUCCESS',
      };
    } catch (error) {
      console.error(
        `❌ LlamaIndex createIndex error for ${documentName}:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Retrieve relevant chunks from vector index using retriever
   */
  async retrieve(
    retrieverId: string,
    query: string,
    topK: number = 5,
  ): Promise<RetrieveResponse> {
    this.checkConfiguration();

    try {
      console.log(
        `🔍 Retrieving from retriever ${retrieverId} for query: "${query.substring(0, 50)}..."`,
      );

      const payload = {
        query,
        top_k: topK,
      };

      const response = await fetch(
        `${this.baseUrl}/retrievers/${retrieverId}/retrieve`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex retrieve error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Retrieval failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ Retrieved ${result.nodes?.length || 0} relevant chunks`);

      return result;
    } catch (error) {
      console.error(`❌ LlamaIndex retrieve error:`, (error as Error).message);
      throw error;
    }
  }

  /**
   * Delete retriever (and associated resources)
   */
  async deleteIndex(retrieverId: string): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      console.log(`🗑️  Deleting retriever: ${retrieverId}`);

      const response = await fetch(
        `${this.baseUrl}/retrievers/${retrieverId}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
          },
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ LlamaIndex deleteIndex error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Index deletion failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ Retriever deleted: ${retrieverId}`);

      return result;
    } catch (error) {
      console.error(
        `❌ LlamaIndex deleteIndex error:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Get content type for file
   */
  private getContentType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    const mimeTypes: { [key: string]: string } = {
      txt: 'text/plain',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ppt: 'application/vnd.ms-powerpoint',
      pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      md: 'text/markdown',
      html: 'text/html',
      rtf: 'application/rtf',
      xml: 'application/xml',
      csv: 'text/csv',
    };
    return mimeTypes[ext || ''] || 'application/octet-stream';
  }
}

export const llamaIndexService = new LlamaIndexService();
