Test Document for Pipeline Sync Verification

This is a test document to verify that the new LlamaIndex pipeline sync workflow is working correctly.

Key Information:
- Document Name: Pipeline Test Document
- Author: Test User
- Purpose: Verify vector embedding creation
- Date: July 11, 2025

Technical Details:
- This document should be processed by LlamaParse
- Then uploaded to LlamaIndex Cloud
- Added to a new pipeline
- Pipeline should be synced to create vector embeddings
- Finally, a retriever should be created for querying

Test Query: "What is the purpose of this document?"
Expected Answer: The document should be found and the purpose should be mentioned as "Verify vector embedding creation"

Pipeline Workflow Steps:
1. Upload file to project
2. Create pipeline
3. Add files to pipeline
4. Sync pipeline (CRITICAL STEP!)
5. Wait for sync completion
6. Create retriever
7. Test retrieval

This document contains enough content to test the complete pipeline sync workflow.
