Pipeline Sync Test Document - Version 2

This is the second test document to verify that the new LlamaIndex pipeline sync workflow is working correctly with unique pipeline names.

Key Information:
- Document Name: Pipeline Sync Test Document V2
- Author: Test User
- Purpose: Verify unique pipeline creation and vector embedding
- Date: July 11, 2025
- Version: 2.0

Technical Details:
- This document should be processed by LlamaParse
- Then uploaded to LlamaIndex Cloud with unique pipeline name
- Added to a new pipeline with timestamp and random ID
- Pipeline should be synced to create vector embeddings
- Finally, a retriever should be created for querying

Test Query: "What is the purpose of this test document?"
Expected Answer: The document should be found and the purpose should be mentioned as "Verify unique pipeline creation and vector embedding"

Pipeline Workflow Steps (Updated):
1. Upload file to project
2. Create pipeline with unique name (timestamp + random IDs)
3. Add files to pipeline
4. Sync pipeline (CRITICAL STEP!)
5. Wait for sync completion
6. Create retriever
7. Test retrieval

This document contains enough content to test the complete pipeline sync workflow with unique naming.
